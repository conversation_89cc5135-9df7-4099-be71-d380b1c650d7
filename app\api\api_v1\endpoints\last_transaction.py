# from typing import Any
# from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
# from ngmy_eia_client import EIAClient
# from core.config import authorization, settings
# from fastapi import APIRouter, Depends
# from utils.get_last_transaction import get_last_transaction

# router = APIRouter()


# @router.get(
#     "/last_transaction",
#     responses={
#         401: {
#             "description": "Invalid JWT Token",
#             "content": {
#                 "application/json": {"example": {"detail": "JWT token is invalid."}}
#             },
#         },
#         200: {
#             "description": "Transactions history list",
#             "content": {"application/json": {"example": {}}},
#         },
#     },
# )
# async def last_transaction(
#     profile: dict = Depends(authorization),
# ) -> Any:
#     """Retrieve subscriber transaction history
#     - **status**: Defines internet usage status

#     Raises:

#         401: If JWT token is invalid

#     Returns:

#         message: Represents operation status

#     """
#     phone_number = profile["phone_number"]
#     customer_type = profile["customer_type"]
#     cow_date = profile["cow_date"]
#     client_name = profile["client_name"]
#     sim_type = profile["sim_type"]
#     language = profile["language"]

#     if sim_type == "fttx":
#         eia_client = EIAClient(
#             settings.EIA_ENDPOINT,
#             settings.EIA_USERNAME,
#             settings.EIA_PASSWORD,
#             phone_number,
#         )

#         customer_profile = await eia_client.fftx_get_subscriber_details(
#             fields=["notification_phone_number"], fttx_id=phone_number
#         )
#         notification_phone_number = customer_profile["notification_phone_number"]

#         phone_number = notification_phone_number

#     res = await get_last_transaction(
#         phone_number, cow_date, client_name, sim_type, language
#     )
#     if res.get("offer_code") and not res.get("offer"):
#         return JSONResponse(
#             status_code=400,
#             content={
#                 "type": "https://my.irancell.ir/errors/payment/last_transaction/invalid_offer",
#                 "title": "The offer is invalid",
#             },
#         )
#     return res
