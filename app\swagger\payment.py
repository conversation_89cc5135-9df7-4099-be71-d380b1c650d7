methods_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "Invalid parameters",
        "content": {
            "application/json": {
                "example": [
                    {"detail": "Invalid service."},
                    {"detail": "GiftBolton service requires beneficiary phone number."},
                    {"detail": "UnDecided service requires beneficiary phone number."},
                    {
                        "detail": "SharedAccConsumerAdd service requires beneficiary phone number.",
                    },
                    {"detail": "Invalid beneficiary phone number"},
                    {"detail": "Beneficiary phone number is required."},
                    {
                        "type": "https://my.irancell.ir/errors/top_up/beneficiary_nubmebr/invalid",
                        "title": "The beneficiary number for top-up is invalid",
                        "detail": f"The phone number *********** can't revice the top-up",
                        "params": {
                            "from_number": "***********",
                            "to_number": "***********",
                        },
                        "message": f"The *********** can't receive recharge or bill payment",
                    },
                    {"detail": "not provider for shared account"},
                    {
                        "detail": "DynamicBolton service requires data_counter and sms_counter, voice_counter, validity_duration"
                    },
                    {"detail": "Invalid offer code"},
                    {"detail": "invalid increased cl amount"},
                    {
                        "detail": "NormalBolton service requires offer code and description"
                    },
                    {
                        "type": "https://my.irancell.ir/errors/payment/pretopost/validation_error",
                        "title": "request for pre-to-post upgrade failed due to validation",
                        "detail": "Shared service is active",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/payment/pretopost/activation_error",
                        "title": "request for pre-to-post upgrade failed due to activation",
                        "detail": "MSISDN is not active",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/payment/pretopost/operation_failed",
                        "title": "request for pre-to-post upgrade failed",
                        "detail": "There was an error with pre-to-post upgrade request, please ask support form online chat",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/payment/paymentmode/validation_error",
                        "title": "There is no payment method available for this request",
                        "detail": "Payment modes not available",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/payment/validation/amount_exceeds_limit",
                        "title": "Payment failes due to amount exeeds the limit",
                        "detail": "Amount for payment is above maximum limit",
                    },
                ]
            }
        },
    },
    200: {
        "description": "Offers list",
        "content": {
            "application/json": {
                "example": {
                    "transaction_id": "777352902242591",
                    "response_time": 1641737217421,
                    "command_status": "OK",
                    "response_msg": "SUCCESS",
                    "order_id": "151d14500494409084118b80949e14be",
                    "reference_id": "888777352902242591",
                    "amount": 10000.0,
                    "available_balance": 8276664.0,
                    "digital_wallet_balance": 87000.0,
                    "service": "NormalBolton",
                    "offer_code": "86350",
                    "loyalty_points_to_redeem": 0.0,
                    "payment_modes": [
                        {
                            "mode": "Bank Payment",
                            "id": "BA",
                            "desc": "Internet Payment",
                            "balance": 0.0,
                            "points": 0.0,
                            "discount": 0,
                            "rf1": None,
                            "rf2": None,
                            "available": "1",
                            "bank_details": [
                                {
                                    "bank_id": 69,
                                    "bank_name": "Pardakht Saman",
                                    "location": "Iran",
                                    "url": "https://IP/WCFServices/AutoChargeNotification/AutoChargeNotification.svc",
                                    "created_date": *************,
                                    "created_user": None,
                                    "bank_code": "PSMN",
                                    "status": 1,
                                    "class_path": None,
                                    "bank_icon": "https://IP/PaymentGateway/ImageLoader?bankId=69",
                                    "bank_retry_count": 3,
                                    "bank_name_farsi": "پرداخت سامان",
                                    "bank_display_order": 1,
                                },
                                {
                                    "bank_id": 5,
                                    "bank_name": "Parsian Bank",
                                    "location": None,
                                    "url": "https://pec.shaparak.ir/pecpaymentgateway/eshopservice.asmx",
                                    "created_date": *************,
                                    "created_user": None,
                                    "bank_code": "PARO",
                                    "status": 1,
                                    "class_path": None,
                                    "bank_icon": "https://IP/PaymentGateway/ImageLoader?bankId=5",
                                    "bank_retry_count": 3,
                                    "bank_name_farsi": "بانک پارسیان",
                                    "bank_display_order": 2,
                                },
                            ],
                            "tax": "900.0",
                            "auth_code": "ca6dd05c-61c9-4b80-8",
                        },
                        {
                            "mode": "Digital Wallet",
                            "id": "DW",
                            "desc": "Digital Wallet",
                            "balance": 87000.0,
                            "points": 0.0,
                            "discount": 0,
                            "rf1": None,
                            "rf2": None,
                            "available": "1",
                            "bank_details": [],
                            "tax": None,
                            "auth_code": None,
                        },
                        {
                            "mode": "Main Balance",
                            "id": "MA",
                            "desc": "Main Account",
                            "balance": 8276664.0,
                            "points": 0.0,
                            "discount": 0,
                            "rf1": None,
                            "rf2": None,
                            "available": "1",
                            "bank_details": [],
                            "tax": None,
                            "auth_code": None,
                        },
                    ],
                }
            }
        },
    },
}

initiate_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "Invalid parameters",
        "content": {
            "application/json": {
                "example": [
                    {"detail": "Bank ID required for BA payment mode"},
                    {
                        "type": "https://my.irancell.ir/errors/payment/pretopost/validation_error",
                        "title": "request for pre-to-post upgrade failed due to validation",
                        "detail": "Shared service is active",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/payment/pretopost/activation_error",
                        "title": "request for pre-to-post upgrade failed due to activation",
                        "detail": "MSISDN is not active",
                    },
                ]
            }
        },
    },
    200: {
        "description": "Offers list",
        "content": {
            "application/json": {
                "example": {
                    "transaction_id": "***************",
                    "response_time": "2022-01-09 18:08:06",
                    "command_status": "OK",
                    "response_msg": "SUCCESS",
                    "order_id": "151d14500494409084118b80949e14be",
                    "reference_id": "888777352902242591",
                    "redirection_url": "https://IP/IPSPG/PaymentGateway?sessionToken=f7bff611-6a49-4903-b26e-6d0fb62c5d6b&id=mIlKGc9gcXvGGBklHUZCn+GhBiQ8y0pozvQ/HzUaskY=",
                }
            }
        },
    },
}
