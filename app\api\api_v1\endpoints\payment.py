import urllib.parse
from datetime import datetime
from typing import Any
import json
from core.settings import settings
from core.config import (
    authorization,
    redis,
    eia_client,
    catalog_client,
    ngpg_client,
)
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request, Header
from fastapi.responses import JSONResponse, RedirectResponse
from ngmi_logging.utils import get_correlation_id, add_params_into_access_log
from schemas import PaymentInitiate
from utils import ngpg_code_map, get_valid_msisdn
from Crypto.Signature import PKCS1_v1_5
from Crypto.Hash import SHA256
from Crypto.PublicKey import RSA
import base64
from broker.helpers import publish_purchase_successful_msg, publish_payment_msg
from swagger.payment import methods_sample_response, initiate_sample_response


router = APIRouter()

http_protocol = "http"

NGPG_RSA_KEY = RSA.importKey(settings.NGPG_PRIVATE_KEY)


@router.get(
    "/methods",
    responses=methods_sample_response,
)
async def methods(
    request: Request,
    background_tasks: BackgroundTasks,
    service: str,
    amount: int,
    offer_code: str = "",
    beneficiary_phone_number: str = "",
    data_counter: str = None,
    voice_counter: str = None,
    sms_counter: str = None,
    validity_duration: str = None,
    increased_cl_amount: int = None,
    sdk_version: str = None,
    fttx_package_reserved: bool = False,
    profile: dict = Depends(authorization),
) -> Any:
    """Retrieve payment modes from NGPG.
    - **service**: Defines NGPG service type(ex:NormalBolton,DirectTopup,etc.)
    - **amount**: Defines payment amount
    - **offer_code**: Defines offer code
    - **beneficiary_phone_number**: Defines benefeciary phone number of payment

    Raises:

        401: If JWT token is invalid
        400: If Benefeciary is invalid
        400: If offer code/description is missing for NormalBolton service
        400: If offer code is not existing in catalog file

    Returns:

        transaction_id: Represents NGPG transaction ID
        response_time: Represents response timestamp
        command_status: Represents NGPG transaction command status
        response_msg: Represents NGPG transaction response message
        order_id: Represents NGPG order ID
        reference_id: Represents NGPG reference ID
        amount: Represents payment request amount
        available_balance: Represents Main balance in case of prepaid subscriber and ACL in case of Post-paid subscriber
        digital_wallet_balance: Represents digital wallet balance
        service: Represents NGPG service
        offer_code: Represents offer code
        loyalty_points_to_redeem: Represents loyalty points required for product purchase. conversion from Transaction Amount
        payment_modes: Represents available payment modes list
        payment_mode.mode: Represents payment mode name
        payment_mode.id: Represents payment mode ID
        payment_mode.desc: Represents payment mode description
        payment_mode.available: Represents if payment mode is available(1 available/0 unavailable)
        payment_mode.balance: Represents available balance for this mode. Balance will be available for cases like main account, dedicated account etc.
        payment_mode.discount: Represents discount amount available
        payment_mode.points: Represents points
        tax: Represents payment tax amount

    """
    if amount < 0:
        return JSONResponse(status_code=422, content={"message": "amount is invalid"})

    if not beneficiary_phone_number:
        if service == "GiftBolton":
            raise HTTPException(
                status_code=400,
                detail="GiftBolton service requires beneficiary phone number.",
            )
        elif service.lower() == "undecided":
            raise HTTPException(
                status_code=400,
                detail="UnDecided service requires beneficiary phone number.",
            )
        elif service.lower() == "SharedAccConsumerAdd":
            raise HTTPException(
                status_code=400,
                detail="SharedAccConsumerAdd service requires beneficiary phone number.",
            )
    else:
        try:
            beneficiary_phone_number = get_valid_msisdn(beneficiary_phone_number)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail="Invalid beneficiary phone number.",
            )

    phone_number = profile["phone_number"]
    sim_type = profile["sim_type"]
    language = profile["language"]

    correlation_id = get_correlation_id()

    add_params_into_access_log(key="amount", value=str(amount))
    add_params_into_access_log(key="target", value=str(service))

    if offer_code and amount == 0:
        lock = await redis.lock_acquire(
            key=f"lock_{phone_number}-{offer_code}",
            expiry=int(settings.RACE_CONDITION_TIME_LIMIT),
        )
        if not lock:
            raise HTTPException(status_code=400, detail="too many requests.")

    channel_name = (
        settings.NGPG_CHANNEL_APP
        if profile["client_name"] == "application"
        else settings.NGPG_CHANNEL_WEB
    )

    if service.lower() == "undecided":
        if not beneficiary_phone_number:
            raise HTTPException(
                status_code=400, detail="Beneficiary phone number is required."
            )
        beneficiary_profile = await eia_client.get_customer_profile(
            beneficiary_phone_number
        )
        if not beneficiary_profile["mtni"]:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/top_up/beneficiary_nubmebr/invalid",
                    "title": "The beneficiary number for top-up is invalid",
                    "detail": f"The phone number {beneficiary_phone_number} can't revice the top-up",
                    "params": {
                        "from_number": phone_number,
                        "to_number": beneficiary_phone_number,
                    },
                    "message": f"The {beneficiary_phone_number} can't receive recharge or bill payment",
                },
            )
        else:
            if beneficiary_profile["customer_type"] == "postpaid":
                service = "BillPayment"
            else:
                service = "DirectTopup"

    if service == "NormalBolton":
        catalog_response = await catalog_client.get_offer_details(
            phone_number, offer_code
        )
        exists_in_catalog = False
        if catalog_response["error_status"]:
            offer_details = await redis.get(
                key=f"PACKAGEINFO_{offer_code}", prefix=settings.CATALOG_REDIS_PREFIX
            )
            if offer_details:
                offer_details = json.loads(offer_details)
                catalog_title = offer_details["title"][language]
                catalog_price = offer_details["price"]
                catalog_service = offer_details["service"]
                exists_in_catalog = True
        else:
            offer_details = catalog_response["offer_details"]
            catalog_title = offer_details["title"][language]
            catalog_price = offer_details["price"]
            catalog_service = service
            exists_in_catalog = True

        if not exists_in_catalog:
            raise HTTPException(status_code=400, detail="Invalid offer")

        if str(amount) != str(catalog_price):
            raise HTTPException(status_code=400, detail="Invalid offer price")

        if str(service) != str(catalog_service):
            raise HTTPException(status_code=400, detail="Invalid service")

        ngpg_response = await ngpg_client.get_payment_modes(
            phone_number=phone_number,
            service=service,
            amount=amount,
            language=language,
            offer_code=offer_code,
            channel_name=channel_name,
            offer_desc=catalog_title,
        )

    elif service == "GiftBolton":
        catalog_response = await catalog_client.get_offer_details(
            phone_number, offer_code
        )
        exists_in_catalog = False
        if catalog_response["error_status"]:
            offer_details = await redis.get(
                key=f"PACKAGEINFO_{offer_code}", prefix=settings.CATALOG_REDIS_PREFIX
            )
            if offer_details:
                offer_details = json.loads(offer_details)
                catalog_title = offer_details["title"][language]
                catalog_price = offer_details["price"]
                catalog_service = offer_details["service"]
                exists_in_catalog = True
        else:
            offer_details = catalog_response["offer_details"]
            catalog_title = offer_details["title"][language]
            catalog_price = offer_details["price"]
            catalog_service = "NormalBolton"
            exists_in_catalog = True

        if not exists_in_catalog:
            raise HTTPException(status_code=400, detail="Invalid offer")

        if str(amount) != str(catalog_price):
            raise HTTPException(status_code=400, detail="Invalid offer price")

        if not str(catalog_service) in ["NormalBolton", "GiftBolton"]:
            raise HTTPException(status_code=400, detail="Invalid service")

        ngpg_response = await ngpg_client.get_payment_modes(
            phone_number=phone_number,
            service=service,
            amount=amount,
            language=language,
            offer_code=offer_code,
            channel_name=channel_name,
            beneficiary_phone_number=beneficiary_phone_number,
            offer_desc=catalog_title,
        )

    elif service == "FTTHBolton":

        if sim_type == "fttx":
            customer_profile = await eia_client.fftx_get_subscriber_details(
                fttx_id=phone_number,
                fields=["notification_phone_number"],
            )
            notification_phone_number = customer_profile["notification_phone_number"]
        else:
            raise HTTPException(
                status_code=400,
                detail="This service is only available for fttx subscribers",
            )

        catalog_response = await catalog_client.get_offer_details(
            phone_number, offer_code
        )

        if catalog_response["error_status"]:
            raise HTTPException(status_code=400, detail="Invalid offer")
        else:
            offer_details = catalog_response["offer_details"]
            catalog_title = offer_details["title"][language]
            catalog_price = offer_details["price"]

        if str(amount) != str(catalog_price):
            raise HTTPException(status_code=400, detail="Invalid offer price")

        ngpg_response = await ngpg_client.get_payment_modes(
            fttx_id=phone_number,
            phone_number=notification_phone_number,
            service=service,
            amount=amount,
            language=language,
            offer_code=offer_code,
            channel_name=channel_name,
            offer_desc=catalog_title,
        )

    elif service in ["DTSOffer", "OnlineBuyable", "BuyyableOffer"]:

        offer_details = await redis.get(
            key=f"PACKAGEINFO_{offer_code}", prefix=settings.CATALOG_REDIS_PREFIX
        )
        if offer_details:
            offer_details = json.loads(offer_details)
            catalog_title = offer_details["title"][language]
            catalog_price = offer_details["price"]
            catalog_service = offer_details["service"]
            exists_in_catalog = True
        else:
            raise HTTPException(status_code=400, detail="Invalid offer")

        if str(amount) != str(catalog_price):
            raise HTTPException(status_code=400, detail="Invalid offer price")

        if str(service) != str(catalog_service):
            raise HTTPException(status_code=400, detail="Invalid service")

        ngpg_response = await ngpg_client.get_payment_modes(
            phone_number=phone_number,
            service=service,
            amount=amount,
            language=language,
            offer_code=offer_code,
            channel_name=channel_name,
            offer_desc=catalog_title,
        )

    elif service in ["DynamicBolton", "MPDynamicBolton"]:

        if not (data_counter or sms_counter or voice_counter or validity_duration):
            raise HTTPException(
                status_code=400,
                detail=f"insufficient for {service}",
            )

        ngpg_response = await ngpg_client.get_payment_modes(
            phone_number=phone_number,
            service=service,
            amount=amount,
            channel_name=channel_name,
            data_counter=data_counter,
            sms_counter=sms_counter,
            voice_counter=voice_counter,
            validity_duration=validity_duration,
        )

    elif service == "SharedBolton":

        eia_response = await eia_client.shared_account_get_status(phone_number)

        if eia_response["error_status"] or not eia_response["data"]["is_provider"]:
            raise HTTPException(
                status_code=400, detail="not provider for shared account"
            )

        catalog_response = await catalog_client.get_offer_details(
            phone_number, offer_code
        )

        if catalog_response["error_status"]:
            raise HTTPException(status_code=400, detail="Invalid offer")
        else:
            offer_details = catalog_response["offer_details"]
            catalog_title = offer_details["title"][language]
            catalog_price = offer_details["price"]

        if str(amount) != str(catalog_price):
            raise HTTPException(status_code=400, detail="Invalid offer price")

        ngpg_response = await ngpg_client.get_payment_modes(
            phone_number=phone_number,
            service=service,
            amount=amount,
            language=language,
            offer_code=offer_code,
            channel_name=channel_name,
            offer_desc=catalog_title,
        )

    elif service == "SharedAccConsumerAdd":
        offer_description = "افزودن زیرشاخه" if language == "fa" else "adding consumer"

        ngpg_response = await ngpg_client.get_payment_modes(
            phone_number=phone_number,
            beneficiary_phone_number=beneficiary_phone_number,
            service=service,
            amount=amount,
            language=language,
            offer_code=offer_code,
            channel_name=channel_name,
            offer_desc=offer_description,
        )

    elif service == "Pre2PostMigration":
        if increased_cl_amount is None:
            raise HTTPException(status_code=400, detail="invalid increased cl amount")

        ngpg_response = await ngpg_client.get_payment_modes(
            phone_number=phone_number,
            service=service,
            amount=amount,
            language=language,
            offer_code=offer_code,
            channel_name=channel_name,
            increased_cl_amount=increased_cl_amount,
        )

    elif service == "WalletCashIn":

        offer_description = "شارژ " + str(amount) + "ریالی"

        ngpg_response = await ngpg_client.get_payment_modes(
            phone_number=phone_number,
            service=service,
            amount=amount,
            language="fa",
            offer_code=offer_code,
            channel_name=channel_name,
            offer_desc=offer_description,
        )

    elif service in [
        "DirectTopup",
        "BillPayment",
        "PostpaidCLIncrease",
        "PerspolisFC",
    ]:
        ngpg_response = await ngpg_client.get_payment_modes(
            phone_number=phone_number,
            service=service,
            amount=amount,
            offer_code=offer_code,
            channel_name=channel_name,
        )
    else:
        raise HTTPException(status_code=400, detail="Invalid service.")

    result_code = ngpg_response["resultCode"]
    result_message = ngpg_response["responseMsg"].lower()
    result_amount = int(ngpg_response["amount"])

    if result_code in ["11113", "149"]:
        return JSONResponse(
            status_code=400,
            content={
                "status": 400,
                "type": "https://my.irancell.ir/errors/payment/pretopost/validation_error",
                "title": "request for pre-to-post upgrade failed due to validation",
                "detail": "Shared service is active",
            },
        )

    if result_code == "113":
        return JSONResponse(
            status_code=400,
            content={
                "status": 400,
                "type": "https://my.irancell.ir/errors/payment/paymentmode/validation_error",
                "title": "There is no payment method available for this request",
                "detail": "Payment modes not available",
            },
        )

    if result_code == "150":
        return JSONResponse(
            status_code=400,
            content={
                "status": 400,
                "type": "https://my.irancell.ir/errors/payment/pretopost/activation_error",
                "title": "request for pre-to-post upgrade failed due to activation",
                "detail": "MSISDN is not active",
            },
        )

    if result_code == "230":
        return JSONResponse(
            status_code=400,
            content={
                "status": 400,
                "type": "https://my.irancell.ir/errors/shared_account/add_consumer/is_active",
                "title": "The shared account service for added SIM is already active. ",
                "detail": "The added number is either provider or consumer in another shared account.",
            },
        )

    if result_code == "11117":
        return JSONResponse(
            status_code=400,
            content={
                "status": 400,
                "type": "https://my.irancell.ir/errors/payment/pretopost/operation_failed",
                "title": "request for pre-to-post upgrade failed",
                "detail": "There was an error with pre-to-post upgrade request, please ask support form online chat",
            },
        )

    if result_code == "181":
        return JSONResponse(
            status_code=400,
            content={
                "status": 400,
                "type": "https://my.irancell.ir/errors/payment/validation/amount_exceeds_limit",
                "title": "Payment failes due to amount exeeds the limit",
                "detail": "Amount for payment is above maximum limit",
            },
        )

    if not result_code == "0":
        raise HTTPException(
            status_code=400,
            detail=ngpg_code_map.get(result_code, "unknown result code"),
        )

    # has_sdk = False
    payment_modes = []
    payment_mode_list = ngpg_response["paymentModeList"]
    if payment_mode_list:
        for pm in payment_mode_list["paymentMode"]:
            if not pm["available"] == "1":
                continue
            bank_details = []
            if pm.get("bankDetails"):
                for bd in pm["bankDetails"]:
                    bank_info = {
                        "bank_id": bd["bankId"],
                        "bank_name": bd["bankName"],
                        "location": bd["location"],
                        "url": bd["url"],
                        "created_date": bd["createdDate"],
                        "created_user": bd["createdUser"],
                        "bank_code": bd["bankCode"],
                        "status": bd["status"],
                        "class_path": bd["classPath"],
                        "bank_icon": bd["bankIcon"],
                        "bank_retry_count": bd["bankRetryCount"],
                        "bank_name_farsi": bd["bankNameFarsi"],
                        "bank_display_order": bd["bankDisplayOrder"],
                    }
                    bank_details.append(bank_info)
            d = {
                "mode": pm["mode"],
                "id": pm["id"],
                "desc": pm["desc"],
                "balance": pm["balance"],
                "points": pm["points"],
                "discount": pm["discount"],
                "rf1": pm["rf1"],
                "rf2": pm["rf2"],
                "available": pm["available"],
                "bank_details": bank_details,
                "tax": pm.get("tax"),
                "auth_code": pm.get("authcode"),
            }
            if d["id"].lower() == "sdk":
                has_sdk = True
            payment_modes.append(d)

    res = {
        "transaction_id": ngpg_response["transactionId"],
        "response_time": ngpg_response["responseTime"],
        "command_status": ngpg_response["commandStatus"],
        "response_msg": ngpg_response["responseMsg"],
        "order_id": ngpg_response["orderId"],
        "reference_id": ngpg_response["referenceId"],
        "amount": ngpg_response["amount"],
        "available_balance": ngpg_response["availableBalance"],
        "digital_wallet_balance": ngpg_response["digitalWalletBalance"],
        "service": ngpg_response["service"],
        "offer_code": ngpg_response["offerCode"],
        "loyalty_points_to_redeem": ngpg_response["loyaltyPointstoRedeem"],
        "payment_modes": payment_modes,
    }

    successful_purchase = False
    result_message = ngpg_response["responseMsg"].lower()
    result_amount = ngpg_response["amount"]
    result_service = ngpg_response["service"]
    result_offer_code = ngpg_response["offerCode"]
    reference_id = ngpg_response["referenceId"]
    order_id = ngpg_response["orderId"]
    if (
        int(result_amount) == 0
        and int(result_code) == 0
        and result_message == "success"
    ):
        successful_purchase = True

    if successful_purchase:
        background_tasks.add_task(
            publish_purchase_successful_msg,
            msisdn=phone_number,
            ngpg_service=service,
            method="GetPaymentModes",
            amount=result_amount,
            reference_id=reference_id,
            correlation_id=correlation_id,
        )

        # if has_sdk:
        base_url = request.url._url.replace(request.url.path, "").replace(
            f"{http_protocol}://", "https://"
        )
        callback_url = f"{base_url}/api/payment/v1/result/landing"
        initiate_body = {
            "channel": channel_name,
            "orderId": order_id,
            "requestTime": datetime.now().strftime("%d%m%Y %H:%M:%S:%f"),
            "mobileNumber": phone_number,
            "callBackUrl": callback_url,
            # "sdkVersion": sdk_version,
            "amount": result_amount,
            "requestType": "NSEAM",
            "referenceId": reference_id,
            # "loyaltyPointtoRedeem": "",
            "mid": settings.NGPG_MID,
            "paymentModeId": "SDK",
            "service": result_service,
            "offerCode": result_offer_code,
            "languageId": "F" if language == "fa" else "E",
            "parameters": {
                "parameter": [{"key": "deviceMobileNumber", "value": phone_number}]
            },
        }
        msg = json.dumps(initiate_body, separators=(",", ":"))
        h = SHA256.new(msg.encode("utf-8"))
        signer = PKCS1_v1_5.new(NGPG_RSA_KEY)
        signature = signer.sign(h)
        decoded_signature = base64.b64encode(signature).decode("utf-8")
        res["sdk_values"] = {"raw": msg, "signed": decoded_signature}

    return res


@router.post(
    "/initiate",
    responses=initiate_sample_response,
)
async def initiate(
    payment: PaymentInitiate,
    request: Request,
    background_tasks: BackgroundTasks,
    source: str = Header(None),
    profile: dict = Depends(authorization),
) -> Any:
    """Initiates payment procedure on NGPG.
    - **service**: Defines NGPG service type(ex:NormalBolton,DirectTopup,etc.)
    - **amount**: Defines payment amount
    - **reference_id**: Defines payment reference ID
    - **order_id**: Defines payment order ID
    - **payment_mode_id**: Defines selected payment mode ID
    - **callback_url**: Defines callback url which will be redirected into
    - **bank_id**: Defines selected bank ID
    - **auto_renew**: Defines if offer has to be renewed or not

    Raises:

        401: If JWT token is invalid
        400: If bank ID is missing for BA payment mode(bank payment)

    Returns:

        transaction_id: Represents NGPG transaction ID
        response_time: Represents response timestamp
        command_status: Represents NGPG transaction command status
        response_msg: Represents NGPG transaction response message
        order_id: Represents NGPG order ID
        reference_id: Represents NGPG reference ID
        redirection_url: Represents url redirection for payment process
    """
    phone_number = profile["phone_number"]
    language = profile["language"]
    sim_type = profile["sim_type"]

    payment = payment.model_dump()
    service = payment["service"]
    amount = payment["amount"]
    offer_code = payment["offer_code"]
    reference_id = payment["reference_id"]
    order_id = payment["order_id"]
    payment_mode_id = payment["payment_mode_id"]
    callback_type = payment["callback_type"]
    bank_id = payment["bank_id"]
    auto_renew = payment["auto_renew"]

    correlation_id = get_correlation_id()
    add_params_into_access_log(key="amount", value=str(amount))
    add_params_into_access_log(key="target", value=str(service))
    add_params_into_access_log(key="internal_ref_id", value=str(reference_id))

    if service in ["OnlineBuyable", "BuyyableOffer"]:
        lock = redis.lock_acquire(
            key=f"lock_{phone_number}-{offer_code}",
            expiry=int(settings.RACE_CONDITION_TIME_LIMIT),
        )
        if not lock:
            raise HTTPException(status_code=400, detail="too many requests.")

    base_url = request.url._url.replace(request.url.path, "").replace(
        f"{http_protocol}://", "https://"
    )
    if callback_type == "native":
        callback_url = f"{base_url}/api/payment/v1/result/landing/{language}"
    else:
        callback_url = f"{base_url}/api/payment/v1/result/app/{language}"

    channel_name = (
        settings.NGPG_CHANNEL_APP
        if profile["client_name"] == "application"
        else settings.NGPG_CHANNEL_WEB
    )

    if service == "FTTHBolton":
        if sim_type == "fttx":
            customer_profile = await eia_client.fftx_get_subscriber_details(
                fttx_id=phone_number,
                fields=["notification_phone_number"],
            )
            phone_number = customer_profile["notification_phone_number"]
        else:
            raise HTTPException(
                status_code=400,
                detail="This service is only available for fttx subscribers",
            )

    ngpg_response = await ngpg_client.capture_payment(
        phone_number=phone_number,
        service=service,
        amount=amount,
        channel_name=channel_name,
        order_id=order_id,
        reference_id=reference_id,
        payment_mode_id=payment_mode_id,
        callback_url=callback_url,
        offer_code=offer_code,
        language=language,
        bank_id=bank_id,
        auto_renew=auto_renew,
    )

    successful_purchase = False
    result_code = ngpg_response["resultCode"]
    result_message = ngpg_response["responseMsg"].lower()

    if (
        not payment_mode_id.lower() == "ba"
        and result_code == "0"
        and result_message == "success"
    ):
        successful_purchase = True

    if result_code in ["11113", "149"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/payment/pretopost/validation_error",
                "title": "request for pre-to-post upgrade failed due to validation",
                "detail": "Shared service is active",
            },
        )

    if result_code == "150":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/payment/pretopost/activation_error",
                "title": "request for pre-to-post upgrade failed due to activation",
                "detail": "MSISDN is not active",
            },
        )
    elif not result_code == "0":
        raise HTTPException(status_code=400, detail=ngpg_response["responseMsg"])

    if successful_purchase:
        background_tasks.add_task(
            publish_purchase_successful_msg,
            msisdn=phone_number,
            ngpg_service=payment["service"],
            method="CapturePayment",
            amount=payment["amount"],
            reference_id=reference_id,
            correlation_id=correlation_id,
        )

    if result_message == "success":
        catalog_response = await catalog_client.get_offer_details(
            phone_number, offer_code
        )
        if not catalog_response["error_status"]:
            offer_details = catalog_response["offer_details"]
            background_tasks.add_task(
                publish_payment_msg,
                service=service,
                channel=profile["client_name"],
                phone_number=phone_number,
                registration_date=profile.get("mtni", {"registration_date": None})[
                    "registration_date"
                ],
                amount=amount,
                offer_code=offer_code,
                offer_type=offer_details["offer_type"],
                offer_sim_type=profile["sim_type"],
                offer_customer_type=profile["customer_type"],
                correlation_id=correlation_id,
                source="trigger" if source == "trigger" else "",
            )

    res = {
        "transaction_id": ngpg_response["transactionId"],
        "response_time": ngpg_response["responseTime"],
        "command_status": ngpg_response["commandStatus"],
        "response_msg": ngpg_response["responseMsg"],
        "order_id": ngpg_response["orderId"],
        "reference_id": ngpg_response["referenceId"],
        "redirection_url": ngpg_response["redirectionURL"],
    }

    return res


@router.post(
    "/result/{type}/{language}", response_class=RedirectResponse, status_code=303
)
async def result(type: str, language: str, request: Request) -> Any:
    # base_url = request.url._url.replace(request.url.path, "")
    data = dict(await request.form())

    if type == "landing":
        params = urllib.parse.urlencode(data)
        redirect_url = f"{settings.CALLBACK_URL_NATIVE}?{params}&lang={language}"
    else:
        redirect_url = f"{settings.CALLBACK_URL_WEB}/{data['statusCode']}/{data.get('clientTransactionId', '')}"

    return redirect_url
