from pydantic import BaseModel


class TransactionStatusInput(BaseModel):
    bankName: str | None = None
    bankPaidAmount: float | None = None
    bankreferenceId: str | None = None
    channel: str | None = None
    dateTime: str | None = None
    ipsReferenceId: str | None = None
    loanPaidAmount: float | None = None
    loyaltyPaidPoints: float | None = None
    mainPaidAmount: float | None = None
    msisdn: str | None = None
    orderId: str | None = None
    parameters: str | None = None
    paymentModeDesc: str | None = None
    service: str | None = None
    status: str | None = None
    statusDesc: str | None = None
    totalPaidAmount: float | None = None
    tpStatusDesc: str | None = None
    tpStatuscode: str | None = None
