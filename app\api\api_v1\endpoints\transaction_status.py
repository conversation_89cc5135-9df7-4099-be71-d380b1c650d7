from typing import Any, Dict
from core.config import logger
from fastapi import APIRouter, BackgroundTasks
from broker.helpers import publish_purchase_successful_msg
from ngmi_logging.utils import (
    set_subject,
    get_correlation_id,
    add_params_into_access_log,
)

router = APIRouter()


@router.post(
    "/internal_final_update",
)
async def transaction_status(
    background_tasks: BackgroundTasks,
    api_input: Dict[str, Any],
) -> Any:
    x_correlation_id = get_correlation_id()

    msisdn = api_input.get("msisdn", "")
    if msisdn:
        set_subject(msisdn)

    total_paid_amount = api_input.get("totalPaidAmount")
    if total_paid_amount is not None:
        add_params_into_access_log(key="amount", value=str(total_paid_amount))

    service = api_input.get("service")
    if service:
        add_params_into_access_log(key="target", value=str(service))

    bank_reference_id = api_input.get("bankreferenceId")
    if bank_reference_id:
        add_params_into_access_log(key="internal_ref_id", value=str(bank_reference_id))

    content = {
        "event": "NGPG.transaction_status",
        "bank_name": api_input.get("bankName"),
        "bank_paid_amount": api_input.get("bankPaidAmount"),
        "bank_reference_id": api_input.get("bankreferenceId"),
        "channel": api_input.get("channel"),
        "date_time": api_input.get("dateTime"),
        "ips_reference_id": api_input.get("ipsReferenceId"),
        "loan_paid_amount": api_input.get("loanPaidAmount"),
        "loyalty_paid_points": api_input.get("loyaltyPaidPoints"),
        "main_paid_amount": api_input.get("mainPaidAmount"),
        "msisdn": api_input.get("msisdn"),
        "order_id": api_input.get("orderId"),
        "parameters": api_input.get("parameters"),
        "paymet_mode_desc": api_input.get("paymentModeDesc"),
        "service": api_input.get("service"),
        "status": api_input.get("status"),
        "status_desc": api_input.get("statusDesc"),
        "total_paid_amount": api_input.get("totalPaidAmount"),
        "tp_status_desc": api_input.get("tpStatusDesc"),
        "tp_statuscode": api_input.get("tpStatuscode"),
        "raw_input": api_input,
    }

    await logger.informer(
        topic="NGPG.transaction_status", service="payment", content=content
    )

    res = api_input.copy()

    if api_input["status"] == "0" and api_input["statusDesc"].lower() == "success":
        background_tasks.add_task(
            publish_purchase_successful_msg,
            msisdn=f"98{api_input['msisdn']}",
            ngpg_service=api_input["service"],
            method="FinalUpdate",
            amount=api_input["totalPaidAmount"],
            reference_id=api_input["ipsReferenceId"],
            correlation_id=x_correlation_id,
        )

    return res








@router.post(
    "/internal_final_update",
)
async def transaction_status(
    background_tasks: BackgroundTasks,
    api_input: Dict[str, Any],
) -> Any:
    x_correlation_id = get_correlation_id()

    api_input = api_input.model_dump()
    
    content = {
        "raw_input": api_input,
    }

    await logger.informer(
        topic="NGPG.transaction_status", service="payment", content=content
    )

    res = api_input.copy()

    if api_input["status"] == "0" and api_input["statusDesc"].lower() == "success":
        background_tasks.add_task(
            publish_purchase_successful_msg,
            msisdn=f"98{api_input['msisdn']}",
            ngpg_service=api_input["service"],
            method="FinalUpdate",
            amount=api_input["totalPaidAmount"],
            reference_id=api_input["ipsReferenceId"],
            correlation_id=x_correlation_id,
        )

    return res




# @router.post(
#     "/internal_final_update",
# )
# async def transaction_status(
#     background_tasks: BackgroundTasks,
#     api_input: TransactionStatusInput,
# ) -> Any:
#     """Transaction Status API"""
#     api_input = api_input.model_dump()
#     x_correlation_id = get_correlation_id()
#     set_subject(api_input["msisdn"])
#     add_params_into_access_log(key="amount", value=str(api_input["totalPaidAmount"]))
#     add_params_into_access_log(key="target", value=str(api_input["service"]))
#     add_params_into_access_log(
#         key="internal_ref_id", value=str(api_input["bankreferenceId"])
#     )

#     content = {
#         "event": "NGPG.transaction_status",
#         "bank_name": api_input["bankName"],
#         "bank_paid_amount": api_input["bankPaidAmount"],
#         "bank_reference_id": api_input["bankreferenceId"],
#         "channel": api_input["channel"],
#         "date_time": api_input["dateTime"],
#         "ips_reference_id": api_input["ipsReferenceId"],
#         "loan_paid_amount": api_input["loanPaidAmount"],
#         "loyalty_paid_points": api_input["loyaltyPaidPoints"],
#         "main_paid_amount": api_input["mainPaidAmount"],
#         "msisdn": api_input["msisdn"],
#         "order_id": api_input["orderId"],
#         "parameters": api_input["parameters"],
#         "paymet_mode_desc": api_input["paymentModeDesc"],
#         "service": api_input["service"],
#         "status": api_input["status"],
#         "status_desc": api_input["statusDesc"],
#         "total_paid_amount": api_input["totalPaidAmount"],
#         "tp_status_desc": api_input["tpStatusDesc"],
#         "tp_statuscode": api_input["tpStatuscode"],
#     }

#     await logger.informer(
#         topic="NGPG.transaction_status", service="payment", content=content
#     )

#     res = {
#         "statusDesc": api_input["statusDesc"],
#         "status": api_input["status"],
#     }

#     if api_input["status"] == "0" and api_input["statusDesc"].lower() == "success":
#         background_tasks.add_task(
#             publish_purchase_successful_msg,
#             msisdn=f"98{api_input['msisdn']}",
#             ngpg_service=api_input["service"],
#             method="FinalUpdate",
#             amount=api_input["totalPaidAmount"],
#             reference_id=api_input["ipsReferenceId"],
#             correlation_id=x_correlation_id,
#         )

#     return res








