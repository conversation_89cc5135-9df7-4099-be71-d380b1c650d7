from typing import Optional

from pydantic import BaseModel
from pydantic import PositiveInt
from pydantic import field_validator



class BeneficiaryPhoneNumber(BaseModel):
    mobile_number: str = ""
    offer_code: str = ""
    

class MethodV3Request(BaseModel):
    service: str
    amount: int
    offer_code: str = ""
    beneficiary_phone_number: str = ""
    beneficiary_phone_numbers: list[BeneficiaryPhoneNumber] = [] 
    data_counter: Optional[str] = None
    voice_counter: Optional[str] = None
    sms_counter: Optional[str] = None
    validity_duration: Optional[str] = None
    increased_cl_amount: Optional[int] = None
    sdk_version: Optional[str] = None
    replace_package: Optional[bool] = True
    otp: str | None = None


    @field_validator("amount")
    def validate_amount(cls, v, values):
        if v < 0:
            raise ValueError("Invalid amount")
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "service": "GiftBolton",
                "amount": 380000,
                "beneficiary_phone_number": "989352002892",
                "offer_code": "PO1994VTH"
            }
        }


class MethodV3Response(BaseModel):
    transaction_id: str
    response_time: int
    command_status: str
    response_msg: str
    order_id: str
    reference_id: str
    amount: float
    available_balance: float
    digital_wallet_balance: float
    service: str
    offer_code: str
    loyalty_points_to_redeem: float
    payment_modes: Optional[list] = None
    sdk_values: Optional[dict] = None


    class Config:
        json_schema_extra = {
            "example": {
                "transaction_id": "900438853469372",
                "response_time": 1727688444102,
                "command_status": "OK",
                "response_msg": "SUCCESS",
                "order_id": "24017956c0ae47d69865aa89869bae29",
                "reference_id": "888900438853469372",
                "amount": 380000.0,
                "available_balance": 14209921.0,
                "digital_wallet_balance": 0.0,
                "service": "GiftBolton",
                "offer_code": "PO1994VTH",
                "loyalty_points_to_redeem": 0.0,
                "payment_modes": []
            }
        }