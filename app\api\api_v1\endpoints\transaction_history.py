from typing import Any
from uuid import UUID
from fastapi import APIRouter, status
from fastapi.responses import JSONResponse
from core.config import ngpg_client
from core.settings import settings
from utils.transaction_history import mapper_transaction_history

router = APIRouter()


@router.get(
    "/token",
)
async def transaction_status(token) -> Any:
    channel_name = settings.NGPG_CHANNEL_WEB
    transaction_history = await ngpg_client.transaction_history_token(
        token=token, ngpg_channel=channel_name
    )
    if int(transaction_history.get("resultCode", 1)) != 0:
        return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "type": "https://my.irancell.ir/errors/top_up/transaction/history/token",
                    "title": "Token not found",
                    "message": "Token not found",
                },
            )
    response = mapper_transaction_history(transaction_history=transaction_history)
    return response
    
