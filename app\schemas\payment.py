from pydantic import BaseModel, field_validator, model_validator


class PaymentInitiate(BaseModel):
    service: str
    amount: int
    offer_code: str
    reference_id: str
    order_id: str
    payment_mode_id: str
    callback_type: str
    bank_id: int | None = None
    option_id: int | None = None
    scheme_id: str | None = None
    auto_renew: bool | None = None

    @field_validator("amount")
    def validate_amount(cls, v, values):
        if v < 0:
            raise ValueError("Invalid amount")
        return v

    @field_validator("bank_id", mode="before")
    def convert_bank_id_to_str(cls, value):
        if isinstance(value, int):
            return str(value)
        return value

    @model_validator(mode="after")
    def offer_code_can_be_blank(cls, values):
        if values.service and values.service == "DirectTopup" and not values.offer_code:
            values.offer_code = ""
            return values
        return values

    class Config:
        json_schema_extra = {
            "example": {
                "service": "NormalBolton",
                "amount": 10000,
                "offer_code": "86350",
                "reference_id": "888777352902242591",
                "order_id": "151d14500494409084118b80949e14be",
                "payment_mode_id": "BA",
                "callback_type": "native",
                "bank_id": "69",
                "auto_renew": False,
            }
        }
