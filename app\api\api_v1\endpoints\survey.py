from typing import Any
from core.settings import settings
from core.config import authorization, iss_client, eia_client, catalog_client
from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from schemas import SurveyInput
from swagger.survey import survey_sample_response

router = APIRouter()


@router.post(
    "/survey",
    responses=survey_sample_response,
)
async def initiate(
    survey_input: SurveyInput, profile: dict = Depends(authorization)
) -> Any:
    """Create Survey for payments.

    Raises:

        401: If JWT token is invalid
        400: If could not create a survey url

    Returns:

        survey url
    """
    survey_input = survey_input.model_dump()
    offer_code = survey_input["offer_code"]
    # service = survey_input["service"]
    amount = survey_input["amount"]

    phone_number = profile["phone_number"]
    sim_type = profile["sim_type"]
    customer_type = profile["customer_type"]
    language = profile["language"]

    bolton_type = "undefined"
    bolton_name = "undefined"

    catalog_response = await catalog_client.get_offer_details(phone_number, offer_code)
    if catalog_response["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/payment/survey/failed",
                "title": f"Invalid offer code: {offer_code}",
            },
        )

    if catalog_response["offer_details"]["category"] == "normal":
        bolton_type = "NORMALBOLTON"
        bolton_name = catalog_response["offer_details"]["title"][profile["language"]]

    customer_profile = await eia_client.get_customer_profile(
        phone_number, fields=["sim_type", "sim_category", "mtni"]
    )

    if customer_profile["mtni"]:
        if customer_profile["sim_category"] == "3g":
            sim_type = "GSM"
        elif customer_profile["sim_type"] == "fd":
            sim_type = "USIM"
        else:
            sim_type = "TDD"
    else:
        sim_type = "NON-MTN"

    survey = await iss_client.create_dedicated_survey(
        phone_number=phone_number,
        channel_name=settings.ISS_CHANNEL_NAME,
        custom_fields={
            "bolton_type": bolton_type,
            "sim_type": sim_type,
            "price": str(amount),
            "bolton_name": bolton_name,
            "sub_type": customer_type,
        },
    )

    if survey["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/payment/survey/failed",
                "title": "Cannot create survey",
            },
        )

    res = {"url": survey["data"] + "?lang="}
    res["url"] += "Fa" if language == "fa" else "En"
    return res
